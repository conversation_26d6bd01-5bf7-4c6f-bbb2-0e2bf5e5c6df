<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import ModeTypeSwitcher from '@/components/ModeTypeSwitcher.vue';
import ProofBlockList from '@/view/other/editKlg/ProofBlockList.vue';

import { KlgDetail, AreaListItem, RefListItem, ProofListItem, ProofCondItem } from '@/utils/type';
import { editProofBlockListApi, params2EditProofBlockList } from '@/apis/path/klg';
import { onMounted, onUpdated, ref, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import router from '@/router';
import ContentRenderer from '@/components/ContentRenderer.vue';
import { highlightCodeBlocks } from '@/utils/highlightCodeBlocks';

const props = defineProps({
  showStep3: Boolean,
  detail: Object as () => KlgDetail,
  areaList: Array as () => AreaListItem[],
  refList: Array as () => RefListItem[],
  proofList: Array as () => ProofListItem[]
});
const emits = defineEmits(['step']);
const proofBlockListRef = ref();
const noteFlag = ref(false);
const curHeaderMode = ref(1); // 0: 陈述 || 1: 证明
const proofForm = ref({
  klgCode: '',
  title: '',
  type: '',
  synList: [] as String[],
  areaList: [] as AreaListItem[],
  refList: [] as RefListItem[],
  proofList: [] as ProofListItem[],
  note: '',
  cnt: ''
});

// 处理转换headerMode
const handleChangeHeader = (mode: number) => {
  curHeaderMode.value = mode;
};
// 处理打开编者规范
const handleOpenRule = () => {
  window.open('/rule', '_blank');
};
// 处理上一步
const handleStep = (step: number) => {
  handleSave(0).then((result) => {
    if (result) {
      emits('step', step);
    }
  });
};
// 处理存草稿
const handleSave = async (status: number, flag?: boolean, toLink?: boolean): Promise<Boolean> => {
  // 在保存前，获取所有编辑器的HTML内容并更新到数据中
  if (proofBlockListRef.value) {
    // 获取所有条件的HTML内容
    const conditionsHtml = proofBlockListRef.value.getAllConditionsHtml();
    conditionsHtml.forEach((blockData: any) => {
      blockData.conditions.forEach((condData: any) => {
        // 更新条件内容为HTML格式
        proofForm.value.proofList[blockData.blockIndex].klgProofCondList[condData.index].cnt =
          condData.html;
      });
    });

    // 获取所有结论的HTML内容
    const conclusionsHtml = proofBlockListRef.value.getAllConclusionsHtml();
    conclusionsHtml.forEach((conclusionData: any) => {
      // 更新结论内容为HTML格式
      proofForm.value.proofList[conclusionData.blockIndex].conclusion = conclusionData.html;
    });
  }

  const params: params2EditProofBlockList = {
    klgCode: proofForm.value.klgCode,
    klgProofBlockList: proofForm.value.proofList,
    status: status
  };
  const res = await editProofBlockListApi(params);
  try {
    if (res.success) {
      if (flag) {
        ElMessage.success('保存成功');
      }
      if (toLink) {
        window.open(`/linkklg?klgCode=${proofForm.value.klgCode}`, '_self');
      }
      return true;
    } else {
      return false;
    }
  } catch (error) {}
  return false;
};
// 处理提交
const handleSubmit = () => {
  proofBlockListRef.value.handleSubmit().then((result) => {
    if (result) {
      handleSave(1);
      handleCancel();
    }
  });
};
watch(
  () => props,
  () => {
    if (props.detail) {
      proofForm.value.klgCode = props.detail.klgCode;
      proofForm.value.title = props.detail.title;
      proofForm.value.type = props.detail.sortTitle;
      proofForm.value.note = props.detail.notice ? props.detail.notice : '';
      proofForm.value.cnt = props.detail.cnt;

      if (props.detail.sysTitles !== '')
        proofForm.value.synList = props.detail.sysTitles.split('@@');
    }
    if (props.areaList) {
      proofForm.value.areaList = props.areaList;
    }
    if (props.refList) {
      proofForm.value.refList = props.refList;
    }
    if (props.proofList && props.proofList?.length !== 0) {
      proofForm.value.proofList = props.proofList;
      // 异步处理HTML到Markdown的转换
      nextTick(async () => {
        if (proofBlockListRef.value) {
          // 处理条件内容的HTML到Markdown转换
          for (let blockIndex = 0; blockIndex < proofForm.value.proofList.length; blockIndex++) {
            const block = proofForm.value.proofList[blockIndex];

            // 处理条件
            for (let condIndex = 0; condIndex < block.klgProofCondList.length; condIndex++) {
              const cond = block.klgProofCondList[condIndex];
              if (cond.sort === 2 && cond.cnt) {
                // 只处理手动输入的条件
                await proofBlockListRef.value.setConditionMarkdown(blockIndex, condIndex, cond.cnt);
              }
            }

            // 处理结论
            if (block.conclusion) {
              await proofBlockListRef.value.setConclusionMarkdown(blockIndex, block.conclusion);
            }
          }
        }
      });
      // console.log('proofForm', proofForm.value.proofList);
    }
  },
  { deep: true, immediate: true }
);

// 取消
const handleCancel = () => {
  const curNode = sessionStorage.getItem('currentNode');
  if (curNode) {
    const node = JSON.parse(curNode);
    router.push(`klg/maintain?areaCode=${node.areaCode}&label=${node.label}`);
  } else {
    const defaultNode = sessionStorage.getItem('defaultNode');
    if (defaultNode) {
      const node = JSON.parse(defaultNode);
      router.push(`klg/maintain?areaCode=${node.areaCode}&label=${node.label}`);
    } else {
      router.push(`klg/maintain`);
    }
  }
};

// 高亮代码
// const highlightCode = () => {
//   hljs.highlightAll();
// };

// onMounted(highlightCode);
// onUpdated(highlightCode);
</script>
<template>
  <div class="wrapper">
    <div class="wrapper-up">
      <div class="wrapper-up-left">
        <div class="header-wrapper">
          <el-steps class="header-steps" :active="2" finish-status="success" align-center>
            <el-step title="基础信息" />
            <el-step title="内容信息" />
            <el-step title="论证信息" />
          </el-steps>
          <span class="header-tips" @click="handleOpenRule">编者规范</span>
        </div>
        <div class="line"></div>
        <div class="info-container">
          <div class="info-header">
            <span class="info-title ck-content" v-html="proofForm.title"> </span>
            <span class="info-type">{{ proofForm.type }}</span>
          </div>
          <div class="info-footer">
            <span class="info-footer-left">
              <span class="info-syn"
                ><span style="white-space: nowrap; margin-right: 40px">同义词</span>
                <div v-if="proofForm.synList.length === 0" style="white-space: nowrap">
                  暂无同义词
                </div>
                <div v-else class="info-syn-text-block">
                  <el-tag
                    effect="plain"
                    :disable-transitions="true"
                    class="info-syn-text"
                    v-for="(item, index) in proofForm.synList"
                    :key="index"
                  >
                    <span class="ck-content" v-html="item"> </span>
                  </el-tag></div
              ></span>
              <span class="info-syn"
                ><span style="white-space: nowrap; margin-right: 25px">所属领域</span>
                <div class="info-syn-text-block">
                  <el-tag
                    effect="plain"
                    :disable-transitions="true"
                    class="info-syn-text"
                    v-for="(item, index) in proofForm.areaList"
                    :key="index"
                  >
                    <span style="white-space: nowrap" class="ck-content" v-html="item.label">
                    </span>
                  </el-tag></div
              ></span>
            </span>
            <span class="note" @click="noteFlag = !noteFlag">
              <img src="@/assets/image/klg/u1695.svg" />
              <span class="note-text">查看编者笔记</span>
            </span>
          </div>
        </div>

        <div class="form-container">
          <div class="line"></div>
          <div class="form-container-header">
            <mode-type-switcher :mode="curHeaderMode" @changeMode="handleChangeHeader" :length="2">
              <template v-slot:mode0> 陈述 </template>
              <template v-slot:mode1> 证明 </template>
            </mode-type-switcher>
          </div>
          <div class="form-container-main">
            <div class="cnt-container" v-if="curHeaderMode === 0">
              <div v-html="proofForm.cnt"></div>
            </div>
            <div class="proof-container" v-else>
              <ProofBlockList
                ref="proofBlockListRef"
                :proof-list="proofForm.proofList"
                @save:draft="handleSave"
              ></ProofBlockList>
            </div>
          </div>
        </div>
      </div>
      <div class="wrapper-up-right" v-if="noteFlag">
        <div class="note-title">
          <span>编者笔记</span>
          <span class="close">
            <el-icon @click="noteFlag = !noteFlag"><Close /></el-icon>
          </span>
        </div>
        <div class="line"></div>
        <div class="content">
          <ContentRenderer
            :content="highlightCodeBlocks(proofForm.note)"
            v-if="proofForm.note"
          ></ContentRenderer>
          <span v-else>暂无内容</span>
        </div>
      </div>
    </div>
    <div class="wrapper-mid" v-if="proofForm.refList.length != 0">
      <el-form :model="proofForm" style="width: 100%; margin-top: 15px" label-width="100">
        <el-form-item label="参考文献">
          <div class="ref-container">
            <div
              class="ref-block"
              style="width: 100%"
              v-for="item in proofForm.refList"
              :key="item.refId"
            >
              <span class="ref-title">{{ item.cntName }}</span>
              <span class="ref-chapter">{{ item.indexPage }}</span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="wrapper-down">
      <div class="footer">
        <CmpButton type="info" @click="handleCancel">取消</CmpButton>
        <CmpButton type="info" @click="handleStep(1)">上一步</CmpButton>
        <CmpButton type="primary" @click="handleSave(0, true)">存草稿</CmpButton>
        <CmpButton type="primary" @click="handleSave(0, true, true)">保存并关联</CmpButton>
        <CmpButton type="primary" @click="handleSubmit">提交</CmpButton>
      </div>
    </div>
  </div>
</template>
<style scoped>
.wrapper {
  color: var(--color-black);
  .wrapper-up {
    width: 1200px;
    min-height: 750px;
    font-family: var(--text-family);
    display: flex;
    flex-direction: row;
    .wrapper-up-left {
      width: 100%;
      background-color: white;
      color: var(--color-black);
      display: flex;
      flex-direction: column;
      .header-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        position: relative;
        /* el-steps 进行状态 */
        ::v-deep(.is-process) {
          color: var(--color-black);
          font-weight: 400;
          font-size: 14px;
        }
        /* el-steps 等待状态 */
        ::v-deep(.is-wait) {
          color: var(--color-invalid);
          font-weight: 400;
          font-size: 14px;
        }
        .header-steps {
          --el-color-success: var(--color-black);
          margin-top: 10px;
          width: 400px;
        }
        .header-tips {
          color: var(--color-primary);
          font-size: 12px;
          cursor: pointer;
          position: absolute;
          right: 40px;
          bottom: 5px;
          &:hover {
            font-weight: 600;
          }
        }
      }
      .info-container {
        margin: 10px 30px;
        .info-header {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          .info-title {
            font-size: 24px;
            font-weight: 600;
            margin-right: 50px;
          }
          .info-type {
            font-size: 14px;
            color: var(--color-grey);
            border: 1px solid var(--color-grey);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            padding: 2px;
            min-width: 80px;
            white-space: nowrap;
          }
        }
        .info-footer {
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
          position: relative;
          .info-footer-left {
            width: 100%;
            display: flex;
            flex-direction: column;
            .info-syn {
              width: 90%;
              font-size: 14px;
              font-weight: 600;
              display: flex;
              flex-direction: row;
              .info-syn-text-block {
                width: 100%;
                white-space: normal;
                overflow-wrap: break-word;
                .info-syn-text {
                  font-weight: 400;
                  white-space: normal;
                }
              }
            }
          }

          .note {
            display: flex;
            align-items: center;
            cursor: pointer;
            position: absolute;
            right: 10px;
            bottom: 0px;
            .note-text {
              text-wrap: nowrap;
              font-size: 12px;
              color: var(--color-primary);
              margin-left: 5px;
            }
          }
        }
      }
      .form-container {
        width: 100%;
        background-color: white;
        display: flex;
        justify-content: center;
        flex-direction: column;
        .form-container-header {
          padding: 10px;
          width: 100%;
          display: flex;
          justify-content: center;
        }
        .form-container-main {
          width: 100%;
          display: flex;
          flex: 1;

          .cnt-container {
            padding: 0 40px;
          }
          .proof-container {
            width: 100%;
            padding: 0 40px;
          }
        }
      }
    }
    .wrapper-up-right {
      margin-left: 10px;
      background-color: white;
      width: 50%;
      .note-title {
        margin-left: 10px;
        font-weight: 600;
        padding: 10px 5px;
        display: flex;
        justify-content: space-between;
        .close {
          &:hover {
            cursor: pointer;
          }
        }
      }
      .content {
        padding: 10px 20px;
        max-height: 100%;
        overflow-y: auto;
      }
    }
  }
  .wrapper-mid {
    background-color: white;
    margin: 0 0;
    margin-top: 10px;
    padding: 5px;

    .ref-container {
      width: 100%;
      .ref-block {
        display: flex;
        flex-direction: row;
        width: 100%;
        margin-bottom: 5px;
        .ref-title {
          background-color: var(--color-light);
          padding: 5px 10px;
          width: 60%;
          border-radius: 2px;
        }
        .ref-chapter {
          margin-left: 10px;
          background-color: var(--color-light);
          width: 30%;
          border-radius: 2px;
          padding: 5px 10px;
        }
      }
    }
  }
  .wrapper-down {
    background-color: white;
    margin-top: 10px;
    padding: 10px;
    .footer {
      margin: 20px;
      width: 100%;
      display: flex;
      justify-content: center;
      gap: 40px;
    }
  }
  .add-block {
    display: flex;
    align-items: center;
    cursor: pointer;
    .add-text {
      margin-left: 5px;
      color: var(--color-primary);
      font-size: 12px;
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
